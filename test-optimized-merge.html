<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化后的空仓列合并测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 12px;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .has-data {
            background-color: #e8f5e8;
        }
        .no-data {
            background-color: #fff3cd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>优化后的空仓列合并逻辑测试</h1>
        <p>测试新的合并规则：同一日期下，有数据的行不合并，只有无数据的行才合并</p>
        
        <div class="test-section">
            <h2>测试场景1: 混合数据（有数据行 + 无数据行）</h2>
            <p>期望：有数据的行保持不变，无数据的行进行合并</p>
            <button onclick="testMixedData()">运行测试</button>
            <div id="mixedResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>测试场景2: 全部有数据</h2>
            <p>期望：所有行都保持不变，不进行任何合并</p>
            <button onclick="testAllHasData()">运行测试</button>
            <div id="allDataResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>测试场景3: 全部无数据</h2>
            <p>期望：保留第一行，其他行清空</p>
            <button onclick="testAllEmpty()">运行测试</button>
            <div id="allEmptyResult" class="test-result"></div>
        </div>
    </div>

    <script>
        // 模拟优化后的processEmptyColumnMerge函数
        function processEmptyColumnMerge(rows, startRow, groupSize, emptyColumnIndex, mergeInfo) {
            // 新的合并逻辑：
            // 1. 有数据的行保持不变，不参与合并
            // 2. 只对没有数据的行进行合并
            
            // 分析组内每行的空仓列数据情况
            const rowAnalysis = [];
            for (let i = 0; i < groupSize; i++) {
                const value = rows[startRow + i][emptyColumnIndex];
                const hasData = value && String(value).trim() !== '';
                rowAnalysis.push({
                    rowIndex: startRow + i,
                    value: value,
                    hasData: hasData
                });
            }
            
            // 找出有数据的行和没有数据的行
            const rowsWithData = rowAnalysis.filter(row => row.hasData);
            const rowsWithoutData = rowAnalysis.filter(row => !row.hasData);
            
            console.log(`空仓列合并分析 - 组内行数: ${groupSize}, 有数据行数: ${rowsWithData.length}, 无数据行数: ${rowsWithoutData.length}`);
            
            // 有数据的行保持不变（不做任何处理）
            // 只处理没有数据的行
            if (rowsWithoutData.length > 1) {
                // 对没有数据的行进行合并：保留第一个无数据行，清空其他无数据行
                const firstEmptyRowIndex = rowsWithoutData[0].rowIndex;
                
                // 清空其他没有数据的行（除了第一个）
                for (let i = 1; i < rowsWithoutData.length; i++) {
                    const rowIndex = rowsWithoutData[i].rowIndex;
                    rows[rowIndex][emptyColumnIndex] = '';
                }
                
                // 记录合并信息（只针对无数据行的合并）
                if (rowsWithoutData.length > 1) {
                    mergeInfo.push({
                        startRow: firstEmptyRowIndex,
                        endRow: rowsWithoutData[rowsWithoutData.length - 1].rowIndex,
                        startCol: emptyColumnIndex,
                        endCol: emptyColumnIndex,
                        type: 'empty-only',
                        description: `合并了${rowsWithoutData.length}个无数据行，保留了${rowsWithData.length}个有数据行`
                    });
                }
                
                console.log(`空仓列合并完成 - 保留了${rowsWithData.length}个有数据行，合并了${rowsWithoutData.length}个无数据行`);
                return true;
            } else {
                console.log(`空仓列无需合并 - 无数据行数量: ${rowsWithoutData.length}`);
                return false;
            }
        }

        function testMixedData() {
            const result = document.getElementById('mixedResult');
            result.innerHTML = '正在测试混合数据场景...\n';

            try {
                // 测试数据：同一日期下，有些行有数据，有些行没数据
                let testRows = [
                    ['2024-01-01', '设备A', '故障1', '是'],      // 有数据
                    ['2024-01-01', '设备B', '故障2', ''],       // 无数据
                    ['2024-01-01', '设备C', '故障3', '否'],      // 有数据
                    ['2024-01-01', '设备D', '故障4', ''],       // 无数据
                    ['2024-01-01', '设备E', '故障5', '']        // 无数据
                ];
                
                const originalData = JSON.parse(JSON.stringify(testRows)); // 深拷贝
                let mergeInfo = [];
                const emptyColumnIndex = 3; // '空仓？'列的索引
                
                const merged = processEmptyColumnMerge(testRows, 0, 5, emptyColumnIndex, mergeInfo);
                
                result.innerHTML += `原始数据:\n`;
                originalData.forEach((row, index) => {
                    const hasData = row[emptyColumnIndex] && String(row[emptyColumnIndex]).trim() !== '';
                    result.innerHTML += `行${index + 1}: [${row.join(', ')}] - ${hasData ? '有数据' : '无数据'}\n`;
                });
                
                result.innerHTML += `\n处理后数据:\n`;
                testRows.forEach((row, index) => {
                    const hasData = row[emptyColumnIndex] && String(row[emptyColumnIndex]).trim() !== '';
                    const wasChanged = JSON.stringify(row) !== JSON.stringify(originalData[index]);
                    result.innerHTML += `行${index + 1}: [${row.join(', ')}] - ${hasData ? '有数据' : '无数据'} ${wasChanged ? '(已修改)' : '(未修改)'}\n`;
                });
                
                result.innerHTML += `\n合并信息: ${JSON.stringify(mergeInfo, null, 2)}\n`;
                
                // 验证结果
                const hasDataRows = [0, 2]; // 应该保持不变的行
                const noDataRows = [1, 3, 4]; // 应该被处理的行
                
                let success = true;
                // 检查有数据的行是否保持不变
                hasDataRows.forEach(index => {
                    if (JSON.stringify(testRows[index]) !== JSON.stringify(originalData[index])) {
                        success = false;
                        result.innerHTML += `❌ 错误：有数据的行${index + 1}被意外修改\n`;
                    }
                });
                
                // 检查无数据行的处理
                if (testRows[1][emptyColumnIndex] !== '' || testRows[3][emptyColumnIndex] !== '' || testRows[4][emptyColumnIndex] !== '') {
                    // 第一个无数据行应该保留，其他无数据行应该被清空
                    const firstEmptyIndex = 1;
                    if (testRows[firstEmptyIndex][emptyColumnIndex] === '' && 
                        testRows[3][emptyColumnIndex] === '' && 
                        testRows[4][emptyColumnIndex] === '') {
                        // 这是正确的
                    } else {
                        success = false;
                        result.innerHTML += `❌ 错误：无数据行的合并处理不正确\n`;
                    }
                }
                
                if (success) {
                    result.innerHTML += '\n✅ 测试通过：有数据行保持不变，无数据行正确合并\n';
                    result.className = 'test-result success';
                } else {
                    result.className = 'test-result error';
                }

            } catch (error) {
                result.innerHTML += `\n❌ 测试出错: ${error.message}\n`;
                result.className = 'test-result error';
            }
        }

        function testAllHasData() {
            const result = document.getElementById('allDataResult');
            result.innerHTML = '正在测试全部有数据场景...\n';

            try {
                let testRows = [
                    ['2024-01-01', '设备A', '故障1', '是'],
                    ['2024-01-01', '设备B', '故障2', '否'],
                    ['2024-01-01', '设备C', '故障3', '是']
                ];
                
                const originalData = JSON.parse(JSON.stringify(testRows));
                let mergeInfo = [];
                
                const merged = processEmptyColumnMerge(testRows, 0, 3, 3, mergeInfo);
                
                result.innerHTML += `处理结果: ${merged ? '进行了合并' : '未进行合并'}\n`;
                result.innerHTML += `合并信息: ${JSON.stringify(mergeInfo)}\n`;
                
                const unchanged = JSON.stringify(testRows) === JSON.stringify(originalData);
                result.innerHTML += `\n${unchanged ? '✅' : '❌'} 数据${unchanged ? '保持不变' : '被意外修改'}\n`;
                
                result.className = unchanged ? 'test-result success' : 'test-result error';

            } catch (error) {
                result.innerHTML += `\n❌ 测试出错: ${error.message}\n`;
                result.className = 'test-result error';
            }
        }

        function testAllEmpty() {
            const result = document.getElementById('allEmptyResult');
            result.innerHTML = '正在测试全部无数据场景...\n';

            try {
                let testRows = [
                    ['2024-01-01', '设备A', '故障1', ''],
                    ['2024-01-01', '设备B', '故障2', ''],
                    ['2024-01-01', '设备C', '故障3', '']
                ];
                
                const originalData = JSON.parse(JSON.stringify(testRows));
                let mergeInfo = [];
                
                const merged = processEmptyColumnMerge(testRows, 0, 3, 3, mergeInfo);
                
                result.innerHTML += `处理结果: ${merged ? '进行了合并' : '未进行合并'}\n`;
                result.innerHTML += `合并信息: ${JSON.stringify(mergeInfo, null, 2)}\n`;
                
                // 检查结果：第一行保持空，其他行也应该是空
                const firstRowEmpty = testRows[0][3] === '';
                const otherRowsEmpty = testRows[1][3] === '' && testRows[2][3] === '';
                
                result.innerHTML += `\n第一行空仓列: "${testRows[0][3]}" ${firstRowEmpty ? '✅' : '❌'}\n`;
                result.innerHTML += `其他行空仓列: "${testRows[1][3]}", "${testRows[2][3]}" ${otherRowsEmpty ? '✅' : '❌'}\n`;
                
                const success = merged && firstRowEmpty && otherRowsEmpty;
                result.innerHTML += `\n${success ? '✅' : '❌'} 全部无数据行${success ? '正确合并' : '合并失败'}\n`;
                
                result.className = success ? 'test-result success' : 'test-result error';

            } catch (error) {
                result.innerHTML += `\n❌ 测试出错: ${error.message}\n`;
                result.className = 'test-result error';
            }
        }
    </script>
</body>
</html>
