<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表头格式测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.3.0/exceljs.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>表头格式测试</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <div class="info">
            <p>此测试页面用于验证Excel表头格式是否符合要求：</p>
            <ul>
                <li><strong>第一行标题</strong>：科陆流水线日常运维及故障处理情况，跨列合并，14号宋体加粗，居中对齐</li>
                <li><strong>第二行列标题</strong>：直接使用输入表格的列标题，12号宋体加粗白色字体，蓝色背景</li>
                <li><strong>数据行</strong>：11号宋体，居中对齐，完整边框</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>生成测试Excel文件</h2>
        <button onclick="generateTestExcel()">生成测试Excel文件</button>
        <p id="status"></p>
    </div>

    <script>
        // 生成测试Excel文件
        async function generateTestExcel() {
            const statusEl = document.getElementById('status');
            statusEl.textContent = '正在生成测试文件...';

            try {
                // 创建测试数据 - 验证表头格式
                const testData = {
                    headers: ['日期', '维护保养情况', '故障处理情况', '空仓？', '备注'],
                    rows: [
                        ['2024-01-01', '正常巡检', '无故障', '否', '设备运行正常'],
                        ['2024-01-02', '清洁维护', '传感器故障已修复', '否', '更换传感器'],
                        ['2024-01-03', '例行检查', '无故障', '是', '临时停机维护']
                    ],
                    mergeInfo: []
                };

                console.log('测试数据:', testData);

                // 创建工作簿
                const workbook = await createFormattedWorkbookWithExcelJS(testData);

                // 下载文件
                await downloadWorkbookWithExcelJS(workbook);

                statusEl.textContent = '测试文件生成成功！请检查：第一行是主标题，第二行是列标题，第三行开始是数据';
            } catch (error) {
                console.error('生成测试文件失败:', error);
                statusEl.textContent = '生成失败: ' + error.message;
            }
        }

        // 使用ExcelJS创建格式化的工作簿
        async function createFormattedWorkbookWithExcelJS(data) {
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('运维日志');

            // 添加主标题行 - 确保标题为"科陆流水线日常运维及故障处理情况"
            const titleRow = worksheet.addRow(['科陆流水线日常运维及故障处理情况']);

            // 合并主标题行 - 跨所有列
            worksheet.mergeCells(1, 1, 1, data.headers.length);

            // 设置主标题样式 - 14号宋体加粗，居中对齐
            const titleCell = worksheet.getCell(1, 1);
            titleCell.font = {
                name: '宋体',
                size: 14,
                bold: true,
                color: { argb: 'FF000000' }
            };
            titleCell.alignment = {
                horizontal: 'center',
                vertical: 'middle'
            };

            // 添加列标题行 - 直接使用输入表格的列标题（经过处理后的headers）
            const headerRow = worksheet.addRow(data.headers);

            // 设置列标题样式 - 12号宋体加粗白色字体，蓝色背景
            headerRow.eachCell((cell, colNumber) => {
                cell.font = {
                    name: '宋体',
                    size: 12,
                    bold: true,
                    color: { argb: 'FFFFFFFF' }  // 白色字体
                };
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FF4472C4' }  // 蓝色背景
                };
                cell.alignment = {
                    horizontal: 'center',
                    vertical: 'middle'
                };
                cell.border = {
                    top: { style: 'thin', color: { argb: 'FF000000' } },
                    left: { style: 'thin', color: { argb: 'FF000000' } },
                    bottom: { style: 'thin', color: { argb: 'FF000000' } },
                    right: { style: 'thin', color: { argb: 'FF000000' } }
                };
            });

            // 添加数据行
            data.rows.forEach(rowData => {
                const dataRow = worksheet.addRow(rowData);

                // 设置数据行样式 - 11号宋体，居中对齐，完整边框
                dataRow.eachCell((cell, colNumber) => {
                    cell.font = {
                        name: '宋体',
                        size: 11,
                        color: { argb: 'FF000000' }
                    };

                    // 根据列类型设置对齐方式
                    const columnHeader = data.headers[colNumber - 1];
                    if (columnHeader && columnHeader.includes('故障处理情况')) {
                        // 故障处理情况列左对齐
                        cell.alignment = {
                            horizontal: 'left',
                            vertical: 'middle',
                            wrapText: true
                        };
                    } else {
                        // 其他列居中对齐
                        cell.alignment = {
                            horizontal: 'center',
                            vertical: 'middle',
                            wrapText: true
                        };
                    }

                    // 完整边框
                    cell.border = {
                        top: { style: 'thin', color: { argb: 'FF000000' } },
                        left: { style: 'thin', color: { argb: 'FF000000' } },
                        bottom: { style: 'thin', color: { argb: 'FF000000' } },
                        right: { style: 'thin', color: { argb: 'FF000000' } }
                    };

                    // 处理日期格式
                    if (columnHeader && columnHeader.includes('日期') && cell.value instanceof Date) {
                        cell.numFmt = 'yyyy-mm-dd';
                    }
                });
            });

            // 设置列宽
            const columnWidths = getColumnWidthsForExcelJS(data.headers);
            columnWidths.forEach((width, index) => {
                worksheet.getColumn(index + 1).width = width;
            });

            // 设置行高
            worksheet.eachRow((row, rowNumber) => {
                row.height = 25; // 25像素行高
            });

            return workbook;
        }

        // 获取ExcelJS的列宽设置
        function getColumnWidthsForExcelJS(headers) {
            const defaultWidth = 15;
            const columnWidthMap = {
                '日期': 15,
                '维护保养情况': 25,
                '故障处理情况': 50,
                '空仓？': 15,
                '空仓': 15,
                '备注': 15
            };

            return headers.map(header => {
                for (const [keyword, width] of Object.entries(columnWidthMap)) {
                    if (header.includes(keyword)) {
                        return width;
                    }
                }
                return defaultWidth;
            });
        }

        // 使用ExcelJS下载工作簿
        async function downloadWorkbookWithExcelJS(workbook) {
            try {
                // 生成文件名 - 使用当前日期作为时间戳
                const today = new Date();
                const dateStr = today.getFullYear() +
                               String(today.getMonth() + 1).padStart(2, '0') +
                               String(today.getDate()).padStart(2, '0');
                const fileName = `表头格式测试${dateStr}.xlsx`;

                // 生成Excel文件的buffer
                const buffer = await workbook.xlsx.writeBuffer();

                // 创建Blob对象
                const blob = new Blob([buffer], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                });

                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                console.log('文件下载成功:', fileName);
            } catch (error) {
                console.error('下载文件失败:', error);
                throw error;
            }
        }
    </script>
</body>
</html>
