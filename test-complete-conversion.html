<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整转换流程测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.3.0/exceljs.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>完整转换流程测试</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <div class="info">
            <p>此测试验证完整的Excel转换流程，确保表头格式正确：</p>
            <ul>
                <li><strong>输入文件</strong>：包含主标题行和列标题行的Excel文件</li>
                <li><strong>期望输出</strong>：第一行是主标题，第二行是列标题，第三行开始是数据</li>
                <li><strong>验证点</strong>：表头格式、列标题内容、数据行位置</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>步骤1：创建测试输入文件</h2>
        <button onclick="createTestInput()">创建测试输入文件</button>
        <p id="inputStatus"></p>
    </div>

    <div class="test-section">
        <h2>步骤2：模拟转换过程</h2>
        <button onclick="testConversion()" id="convertBtn" disabled>测试转换过程</button>
        <p id="convertStatus"></p>
    </div>

    <script>
        let testInputData = null;

        // 创建测试输入文件
        async function createTestInput() {
            const statusEl = document.getElementById('inputStatus');
            statusEl.textContent = '正在创建测试输入文件...';
            
            try {
                const workbook = new ExcelJS.Workbook();
                const worksheet = workbook.addWorksheet('运维日志');

                // 添加主标题行（模拟输入文件的格式）
                const titleRow = worksheet.addRow(['科陆流水线日常运维及故障处理情况']);
                worksheet.mergeCells(1, 1, 1, 5);

                // 添加列标题行（包含记录人列，用于测试过滤功能）
                const headers = ['日期', '维护保养情况', '故障处理情况', '空仓？', '记录人'];
                worksheet.addRow(headers);

                // 添加测试数据
                const testData = [
                    ['2024-01-01', '正常巡检', '无故障', '否', '张三'],
                    ['2024-01-02', '清洁维护', '传感器故障已修复', '否', '李四'],
                    ['2024-01-03', '例行检查', '无故障', '是', '王五']
                ];

                testData.forEach(row => {
                    worksheet.addRow(row);
                });

                // 保存测试数据供转换使用
                testInputData = workbook;

                statusEl.innerHTML = '<div class="success">测试输入文件创建成功！可以进行转换测试。</div>';
                document.getElementById('convertBtn').disabled = false;
                
            } catch (error) {
                console.error('创建测试输入文件失败:', error);
                statusEl.innerHTML = '<div class="error">创建失败: ' + error.message + '</div>';
            }
        }

        // 测试转换过程
        async function testConversion() {
            const statusEl = document.getElementById('convertStatus');
            statusEl.textContent = '正在测试转换过程...';
            
            try {
                if (!testInputData) {
                    throw new Error('请先创建测试输入文件');
                }

                // 模拟转换过程
                console.log('开始转换过程...');
                
                // 1. 处理输入数据
                const processedData = processWorkbookDataFromExcelJS(testInputData);
                console.log('处理后的数据:', processedData);

                // 2. 创建格式化的输出文件
                const outputWorkbook = await createFormattedWorkbookWithExcelJS(processedData);
                
                // 3. 下载输出文件
                await downloadWorkbookWithExcelJS(outputWorkbook, '转换测试结果');

                statusEl.innerHTML = '<div class="success">转换测试成功！请检查下载的文件：<br>' +
                    '- 第一行：科陆流水线日常运维及故障处理情况（合并单元格）<br>' +
                    '- 第二行：列标题（蓝色背景，白色字体）<br>' +
                    '- 第三行开始：数据行</div>';
                
            } catch (error) {
                console.error('转换测试失败:', error);
                statusEl.innerHTML = '<div class="error">转换失败: ' + error.message + '</div>';
            }
        }

        // 引入主程序的转换函数
        // 使用ExcelJS处理工作簿数据
        function processWorkbookDataFromExcelJS(workbook) {
            // 获取第一个工作表
            const worksheet = workbook.worksheets[0];
            if (!worksheet) {
                throw new Error('工作簿中没有找到工作表');
            }

            // 提取数据 - 特别处理合并单元格
            const data = [];
            worksheet.eachRow((row, rowNumber) => {
                const rowData = [];

                // 获取行的实际列数
                const actualColumnCount = row.actualCellCount || row.cellCount;

                // 遍历所有可能的列
                for (let colNumber = 1; colNumber <= Math.max(actualColumnCount, 10); colNumber++) {
                    const cell = row.getCell(colNumber);

                    // 处理日期类型
                    if (cell.type === ExcelJS.ValueType.Date) {
                        rowData[colNumber - 1] = cell.value;
                    } else {
                        rowData[colNumber - 1] = cell.value;
                    }
                }

                data.push(rowData);
            });

            if (data.length === 0) {
                throw new Error('工作表中没有数据');
            }

            // 智能检测表头位置
            let headers = null;
            let dataStartRow = 1;

            console.log('原始数据前3行:', data.slice(0, 3));

            // 检查第一行是否是合并的标题行
            if (data[0]) {
                const firstRowNonEmptyCells = data[0].filter(cell =>
                    cell !== null && cell !== undefined && String(cell).trim() !== ''
                );

                console.log('第一行非空单元格:', firstRowNonEmptyCells);

                // 检查第一行是否所有非空单元格都是相同的且包含"科陆流水线"
                // 这表明是合并单元格的主标题行
                if (firstRowNonEmptyCells.length > 0) {
                    const firstCellValue = String(firstRowNonEmptyCells[0]).trim();
                    const allSame = firstRowNonEmptyCells.every(cell =>
                        String(cell).trim() === firstCellValue
                    );

                    if (allSame && firstCellValue.includes('科陆流水线')) {
                        // 第一行是标题行，第二行是列标题
                        headers = data[1];
                        dataStartRow = 2;
                        console.log('检测到主标题行（合并单元格），使用第二行作为列标题');
                    } else {
                        // 第一行就是列标题
                        headers = data[0];
                        dataStartRow = 1;
                        console.log('第一行作为列标题');
                    }
                } else {
                    throw new Error('第一行没有有效数据');
                }
            } else {
                throw new Error('数据为空');
            }

            if (!headers || headers.length === 0) {
                throw new Error('无法读取表头信息');
            }

            // 清理和过滤列标题
            headers = headers.map(header => String(header || '').trim()).filter(h => h);

            if (headers.length === 0) {
                throw new Error('未找到有效的列标题');
            }

            console.log('检测到的列标题:', headers);
            console.log('数据开始行:', dataStartRow);

            // 过滤掉'记录人'列
            const filteredHeaders = [];
            const columnIndexMap = new Map();

            headers.forEach((header, index) => {
                if (!header.includes('记录人')) {
                    columnIndexMap.set(filteredHeaders.length, index);
                    filteredHeaders.push(header);
                }
            });

            // 添加新列
            filteredHeaders.push('备注', '维护保养情况');

            console.log('处理后的列标题:', filteredHeaders);

            // 处理数据行 - 使用正确的数据开始行
            const dataRows = data.slice(dataStartRow).filter(row =>
                row.some(cell => cell !== null && cell !== undefined && String(cell).trim() !== '')
            );

            console.log('有效数据行数:', dataRows.length);

            if (dataRows.length === 0) {
                throw new Error('没有找到有效的数据行');
            }

            // 转换数据行
            const processedRows = dataRows.map(row => {
                const newRow = [];

                // 复制原有列数据（除了记录人列）
                columnIndexMap.forEach((colIndex, newColIndex) => {
                    let cellValue = row[colIndex] || '';
                    newRow.push(cellValue);
                });

                // 添加新列数据
                newRow.push('已解决'); // 备注列
                newRow.push(''); // 维护保养情况列

                return newRow;
            });

            return {
                headers: filteredHeaders,
                rows: processedRows,
                mergeInfo: []
            };
        }

        // 其他必要的函数...
        async function createFormattedWorkbookWithExcelJS(data) {
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('运维日志');

            // 添加主标题行
            const titleRow = worksheet.addRow(['科陆流水线日常运维及故障处理情况']);
            worksheet.mergeCells(1, 1, 1, data.headers.length);

            // 设置主标题样式
            const titleCell = worksheet.getCell(1, 1);
            titleCell.font = { name: '宋体', size: 14, bold: true, color: { argb: 'FF000000' } };
            titleCell.alignment = { horizontal: 'center', vertical: 'middle' };

            // 添加列标题行
            const headerRow = worksheet.addRow(data.headers);
            headerRow.eachCell((cell, colNumber) => {
                cell.font = { name: '宋体', size: 12, bold: true, color: { argb: 'FFFFFFFF' } };
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } };
                cell.alignment = { horizontal: 'center', vertical: 'middle' };
                cell.border = {
                    top: { style: 'thin', color: { argb: 'FF000000' } },
                    left: { style: 'thin', color: { argb: 'FF000000' } },
                    bottom: { style: 'thin', color: { argb: 'FF000000' } },
                    right: { style: 'thin', color: { argb: 'FF000000' } }
                };
            });

            // 添加数据行
            data.rows.forEach(rowData => {
                const dataRow = worksheet.addRow(rowData);
                dataRow.eachCell((cell, colNumber) => {
                    cell.font = { name: '宋体', size: 11, color: { argb: 'FF000000' } };
                    cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                    cell.border = {
                        top: { style: 'thin', color: { argb: 'FF000000' } },
                        left: { style: 'thin', color: { argb: 'FF000000' } },
                        bottom: { style: 'thin', color: { argb: 'FF000000' } },
                        right: { style: 'thin', color: { argb: 'FF000000' } }
                    };
                });
            });

            // 设置行高
            worksheet.eachRow((row, rowNumber) => {
                row.height = 25;
            });

            return workbook;
        }

        async function downloadWorkbookWithExcelJS(workbook, prefix = '转换测试') {
            const today = new Date();
            const dateStr = today.getFullYear() +
                           String(today.getMonth() + 1).padStart(2, '0') +
                           String(today.getDate()).padStart(2, '0');
            const fileName = `${prefix}${dateStr}.xlsx`;

            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });

            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            console.log('文件下载成功:', fileName);
        }
    </script>
</body>
</html>
