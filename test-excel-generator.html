<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试Excel文件生成器</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试Excel文件生成器</h1>
        <p>生成用于验证列顺序和空仓列合并修复的测试文件</p>
        
        <button onclick="createTestFile1()">生成测试文件1 - 空仓列有不同数据</button>
        <button onclick="createTestFile2()">生成测试文件2 - 空仓列数据相同</button>
        <button onclick="createTestFile3()">生成测试文件3 - 空仓列为空</button>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        function createTestFile1() {
            // 创建包含不同空仓列数据的测试文件
            const data = [
                ['科陆流水线日常运维及故障处理情况'],
                ['序号', '日期', '设备名称', '故障处理情况', '空仓？', '记录人'],
                [1, '2024-01-01', '设备A', '电源故障，已更换电源模块', '是', '张三'],
                [2, '2024-01-01', '设备B', '传感器异常，已重新校准', '否', '李四'],
                [3, '2024-01-01', '设备C', '网络连接中断，已修复', '是', '王五'],
                [4, '2024-01-02', '设备D', '软件升级完成', '否', '赵六'],
                [5, '2024-01-02', '设备E', '定期维护检查', '是', '钱七']
            ];
            
            createAndDownloadExcel(data, 'test-different-empty.xlsx');
            showPreview(data, '测试文件1 - 空仓列有不同数据（不应合并）');
        }

        function createTestFile2() {
            // 创建空仓列数据相同的测试文件
            const data = [
                ['科陆流水线日常运维及故障处理情况'],
                ['序号', '日期', '设备名称', '故障处理情况', '空仓？', '记录人'],
                [1, '2024-01-01', '设备A', '电源故障，已更换电源模块', '是', '张三'],
                [2, '2024-01-01', '设备B', '传感器异常，已重新校准', '是', '李四'],
                [3, '2024-01-01', '设备C', '网络连接中断，已修复', '是', '王五'],
                [4, '2024-01-02', '设备D', '软件升级完成', '否', '赵六'],
                [5, '2024-01-02', '设备E', '定期维护检查', '否', '钱七']
            ];
            
            createAndDownloadExcel(data, 'test-same-empty.xlsx');
            showPreview(data, '测试文件2 - 空仓列数据相同（应该合并）');
        }

        function createTestFile3() {
            // 创建空仓列为空的测试文件
            const data = [
                ['科陆流水线日常运维及故障处理情况'],
                ['序号', '日期', '设备名称', '故障处理情况', '空仓？', '记录人'],
                [1, '2024-01-01', '设备A', '电源故障，已更换电源模块', '', '张三'],
                [2, '2024-01-01', '设备B', '传感器异常，已重新校准', '', '李四'],
                [3, '2024-01-01', '设备C', '网络连接中断，已修复', '', '王五'],
                [4, '2024-01-02', '设备D', '软件升级完成', '', '赵六'],
                [5, '2024-01-02', '设备E', '定期维护检查', '', '钱七']
            ];
            
            createAndDownloadExcel(data, 'test-empty.xlsx');
            showPreview(data, '测试文件3 - 空仓列为空（应该合并）');
        }

        function createAndDownloadExcel(data, filename) {
            // 创建工作簿
            const wb = XLSX.utils.book_new();
            
            // 创建工作表
            const ws = XLSX.utils.aoa_to_sheet(data);
            
            // 设置合并单元格（主标题行）
            ws['!merges'] = [
                { s: { r: 0, c: 0 }, e: { r: 0, c: 5 } }
            ];
            
            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(wb, ws, '运维日志');
            
            // 下载文件
            XLSX.writeFile(wb, filename);
        }

        function showPreview(data, title) {
            const result = document.getElementById('result');
            let html = `<h3>${title}</h3>`;
            html += '<table>';
            
            data.forEach((row, index) => {
                html += '<tr>';
                if (index === 0) {
                    // 主标题行
                    html += `<td colspan="6" style="text-align: center; font-weight: bold;">${row[0]}</td>`;
                } else {
                    // 数据行
                    row.forEach(cell => {
                        html += `<td>${cell}</td>`;
                    });
                }
                html += '</tr>';
            });
            
            html += '</table>';
            result.innerHTML = html;
        }
    </script>
</body>
</html>
