<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空仓列数据诊断工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .file-input {
            margin: 20px 0;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            text-align: center;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            max-height: 600px;
            overflow-y: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 12px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 4px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        .has-data {
            background-color: #e8f5e8;
        }
        .no-data {
            background-color: #fff3cd;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        .stats {
            background-color: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>空仓列数据诊断工具</h1>
        <p>上传Excel文件，分析空仓列的数据识别情况</p>
        
        <div class="file-input">
            <input type="file" id="fileInput" accept=".xlsx" onchange="handleFile(event)">
            <p>选择要诊断的Excel文件</p>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function handleFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    
                    // 获取第一个工作表
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    
                    // 转换为JSON数据
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                    
                    diagnoseEmptyColumn(jsonData);
                } catch (error) {
                    showError('文件读取失败: ' + error.message);
                }
            };
            reader.readAsArrayBuffer(file);
        }

        function diagnoseEmptyColumn(data) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            
            let html = '<h2>空仓列数据诊断结果</h2>';
            
            // 检查基本结构
            if (data.length < 3) {
                html += '<p class="error">❌ 文件结构不正确，行数不足</p>';
                resultDiv.innerHTML = html;
                return;
            }
            
            // 找到列标题行
            let headerRowIndex = -1;
            let headers = [];
            
            // 检查前几行，找到包含"空仓"的行作为标题行
            for (let i = 0; i < Math.min(5, data.length); i++) {
                const row = data[i];
                if (row && row.some(cell => cell && String(cell).includes('空仓'))) {
                    headerRowIndex = i;
                    headers = row;
                    break;
                }
            }
            
            if (headerRowIndex === -1) {
                html += '<p class="error">❌ 未找到包含"空仓"列的标题行</p>';
                resultDiv.innerHTML = html;
                return;
            }
            
            html += `<div class="stats">`;
            html += `<p><strong>标题行位置:</strong> 第${headerRowIndex + 1}行</p>`;
            html += `<p><strong>列标题:</strong> [${headers.join(', ')}]</p>`;
            html += `</div>`;
            
            // 找到空仓列的索引
            const emptyColumnIndex = findColumnIndex(headers, ['空仓', '空仓？']);
            
            if (emptyColumnIndex === -1) {
                html += '<p class="error">❌ 未找到空仓列</p>';
                resultDiv.innerHTML = html;
                return;
            }
            
            html += `<div class="stats">`;
            html += `<p><strong>空仓列位置:</strong> 第${emptyColumnIndex + 1}列 (${headers[emptyColumnIndex]})</p>`;
            html += `</div>`;
            
            // 分析数据行
            const dataRows = data.slice(headerRowIndex + 1);
            let totalRows = 0;
            let hasDataCount = 0;
            let noDataCount = 0;
            let emptyColumnAnalysis = [];
            
            dataRows.forEach((row, index) => {
                if (row && row.length > 0) {
                    totalRows++;
                    const emptyValue = row[emptyColumnIndex];
                    const hasData = emptyValue && String(emptyValue).trim() !== '';
                    
                    if (hasData) {
                        hasDataCount++;
                    } else {
                        noDataCount++;
                    }
                    
                    emptyColumnAnalysis.push({
                        rowIndex: headerRowIndex + 1 + index + 1, // 显示行号（从1开始）
                        originalIndex: index,
                        value: emptyValue,
                        hasData: hasData,
                        valueType: typeof emptyValue,
                        valueLength: emptyValue ? String(emptyValue).length : 0,
                        trimmedValue: emptyValue ? String(emptyValue).trim() : '',
                        row: row
                    });
                }
            });
            
            html += `<div class="stats">`;
            html += `<p><strong>总数据行数:</strong> ${totalRows}</p>`;
            html += `<p><strong>有数据行数:</strong> <span class="success">${hasDataCount}</span></p>`;
            html += `<p><strong>无数据行数:</strong> <span class="warning">${noDataCount}</span></p>`;
            html += `</div>`;
            
            // 显示有数据的行详情
            const hasDataRows = emptyColumnAnalysis.filter(item => item.hasData);
            html += `<h3>有数据的行详情 (${hasDataRows.length}行)</h3>`;
            
            if (hasDataRows.length > 0) {
                html += '<table>';
                html += '<tr><th>行号</th><th>空仓值</th><th>类型</th><th>长度</th><th>去空格后</th><th>完整行数据</th></tr>';
                
                hasDataRows.forEach(item => {
                    html += '<tr class="has-data">';
                    html += `<td>${item.rowIndex}</td>`;
                    html += `<td>"${item.value}"</td>`;
                    html += `<td>${item.valueType}</td>`;
                    html += `<td>${item.valueLength}</td>`;
                    html += `<td>"${item.trimmedValue}"</td>`;
                    html += `<td>[${item.row.slice(0, 8).join(', ')}...]</td>`;
                    html += '</tr>';
                });
                
                html += '</table>';
            } else {
                html += '<p class="warning">没有找到有数据的行</p>';
            }
            
            // 显示前20个无数据行的详情
            const noDataRows = emptyColumnAnalysis.filter(item => !item.hasData);
            html += `<h3>无数据的行详情 (显示前20行，共${noDataRows.length}行)</h3>`;
            
            if (noDataRows.length > 0) {
                html += '<table>';
                html += '<tr><th>行号</th><th>空仓值</th><th>类型</th><th>长度</th><th>去空格后</th><th>完整行数据</th></tr>';
                
                noDataRows.slice(0, 20).forEach(item => {
                    html += '<tr class="no-data">';
                    html += `<td>${item.rowIndex}</td>`;
                    html += `<td>"${item.value}"</td>`;
                    html += `<td>${item.valueType}</td>`;
                    html += `<td>${item.valueLength}</td>`;
                    html += `<td>"${item.trimmedValue}"</td>`;
                    html += `<td>[${item.row.slice(0, 8).join(', ')}...]</td>`;
                    html += '</tr>';
                });
                
                html += '</table>';
                
                if (noDataRows.length > 20) {
                    html += `<p class="warning">还有${noDataRows.length - 20}行无数据行未显示</p>`;
                }
            }
            
            // 分析可能的问题
            html += '<h3>问题分析</h3>';
            html += '<ul>';
            
            if (hasDataCount < 17) {
                html += `<li class="error">检测到的有数据行数(${hasDataCount})少于预期(17)，可能的原因：</li>`;
                html += '<ul>';
                html += '<li>空仓列索引识别错误</li>';
                html += '<li>数据中包含不可见字符或特殊空格</li>';
                html += '<li>数据类型不是字符串</li>';
                html += '<li>列标题识别错误</li>';
                html += '</ul>';
            }
            
            // 检查是否有特殊的空值
            const specialEmptyValues = noDataRows.filter(item => 
                item.value !== undefined && item.value !== null && item.value !== ''
            );
            
            if (specialEmptyValues.length > 0) {
                html += `<li class="warning">发现${specialEmptyValues.length}个特殊的"空"值：</li>`;
                html += '<ul>';
                specialEmptyValues.slice(0, 5).forEach(item => {
                    html += `<li>行${item.rowIndex}: "${item.value}" (类型: ${item.valueType}, 长度: ${item.valueLength})</li>`;
                });
                html += '</ul>';
            }
            
            html += '</ul>';
            
            resultDiv.innerHTML = html;
        }

        function findColumnIndex(headers, keywords) {
            for (let i = 0; i < headers.length; i++) {
                const header = String(headers[i]).toLowerCase();
                if (keywords.some(keyword => header.includes(keyword.toLowerCase()))) {
                    return i;
                }
            }
            return -1;
        }

        function showError(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `<p class="error">${message}</p>`;
        }
    </script>
</body>
</html>
