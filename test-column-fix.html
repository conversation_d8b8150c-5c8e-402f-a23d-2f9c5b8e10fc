<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>列顺序和空仓列修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>列顺序和空仓列修复测试</h1>
        
        <div class="test-section">
            <h2>测试1: 列顺序验证</h2>
            <p>验证维护保养情况列是否正确插入到日期和故障处理情况列之间</p>
            <button onclick="testColumnOrder()">运行测试</button>
            <div id="columnOrderResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>测试2: 空仓列合并逻辑验证</h2>
            <p>验证空仓列只在数据为空或完全相同时才合并</p>
            <button onclick="testEmptyColumnMerge()">运行测试</button>
            <div id="emptyColumnResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>测试3: 完整数据处理验证</h2>
            <p>使用模拟数据验证完整的处理流程</p>
            <button onclick="testCompleteProcessing()">运行测试</button>
            <div id="completeResult" class="test-result"></div>
        </div>
    </div>

    <script>
        // 模拟必要的函数
        function formatDateValue(dateValue) {
            if (!dateValue) return null;
            if (dateValue instanceof Date) return dateValue;
            if (typeof dateValue === 'number') {
                // Excel日期序列号转换
                const excelEpoch = new Date(1900, 0, 1);
                const days = dateValue - 2; // Excel的日期计算偏差
                return new Date(excelEpoch.getTime() + days * 24 * 60 * 60 * 1000);
            }
            if (typeof dateValue === 'string') {
                const parsed = new Date(dateValue);
                return isNaN(parsed.getTime()) ? dateValue : parsed;
            }
            return dateValue;
        }

        function findDateColumnIndex(headers) {
            const dateKeywords = ['日期', 'date', '时间', 'time', '日', '月', '年'];
            for (let i = 0; i < headers.length; i++) {
                const header = String(headers[i]).toLowerCase();
                if (dateKeywords.some(keyword => header.includes(keyword))) {
                    return i;
                }
            }
            return -1;
        }

        function findColumnIndex(headers, keywords) {
            for (let i = 0; i < headers.length; i++) {
                const header = String(headers[i]).toLowerCase();
                if (keywords.some(keyword => header.includes(keyword.toLowerCase()))) {
                    return i;
                }
            }
            return -1;
        }

        // 测试列顺序
        function testColumnOrder() {
            const result = document.getElementById('columnOrderResult');
            result.innerHTML = '正在测试列顺序...\n';

            try {
                // 模拟原始表头
                const originalHeaders = ['序号', '日期', '设备名称', '故障处理情况', '空仓？', '记录人'];
                
                // 模拟列处理逻辑
                const filteredHeaders = [];
                const columnIndexMap = [];

                // 找到关键列的索引
                let dateColumnIndex = -1;
                let failureColumnIndex = -1;
                
                originalHeaders.forEach((header, index) => {
                    if (!header.includes('记录人')) {
                        const headerStr = String(header).toLowerCase();
                        // 检查是否是日期列
                        if (headerStr.includes('日期') || headerStr.includes('date') || headerStr.includes('时间')) {
                            dateColumnIndex = filteredHeaders.length;
                        }
                        // 检查是否是故障处理情况列
                        if (headerStr.includes('故障')) {
                            failureColumnIndex = filteredHeaders.length;
                        }
                        
                        columnIndexMap.push(index);
                        filteredHeaders.push(header);
                    }
                });

                // 在日期列和故障处理情况列之间插入维护保养情况列
                let maintenanceInsertIndex = filteredHeaders.length; // 默认插入到末尾
                
                if (dateColumnIndex !== -1 && failureColumnIndex !== -1) {
                    // 如果找到了日期列和故障处理情况列，在它们之间插入
                    maintenanceInsertIndex = Math.max(dateColumnIndex + 1, failureColumnIndex);
                } else if (dateColumnIndex !== -1) {
                    // 如果只找到日期列，在日期列后插入
                    maintenanceInsertIndex = dateColumnIndex + 1;
                }
                
                // 插入维护保养情况列
                filteredHeaders.splice(maintenanceInsertIndex, 0, '维护保养情况');
                
                // 添加备注列到末尾
                filteredHeaders.push('备注');

                result.innerHTML += `原始表头: ${originalHeaders.join(', ')}\n`;
                result.innerHTML += `处理后表头: ${filteredHeaders.join(', ')}\n`;
                result.innerHTML += `日期列索引: ${dateColumnIndex}\n`;
                result.innerHTML += `故障处理情况列索引: ${failureColumnIndex}\n`;
                result.innerHTML += `维护保养情况列插入位置: ${maintenanceInsertIndex}\n`;

                // 验证结果
                const dateIndex = filteredHeaders.indexOf('日期');
                const maintenanceIndex = filteredHeaders.indexOf('维护保养情况');
                const failureIndex = filteredHeaders.indexOf('故障处理情况');

                if (dateIndex < maintenanceIndex && maintenanceIndex < failureIndex) {
                    result.innerHTML += '\n✅ 测试通过: 维护保养情况列正确插入到日期和故障处理情况列之间\n';
                    result.className = 'test-result success';
                } else {
                    result.innerHTML += '\n❌ 测试失败: 列顺序不正确\n';
                    result.className = 'test-result error';
                }

            } catch (error) {
                result.innerHTML += `\n❌ 测试出错: ${error.message}\n`;
                result.className = 'test-result error';
            }
        }

        // 测试空仓列合并逻辑
        function testEmptyColumnMerge() {
            const result = document.getElementById('emptyColumnResult');
            result.innerHTML = '正在测试空仓列合并逻辑...\n';

            try {
                // 模拟processEmptyColumnMerge函数
                function processEmptyColumnMerge(rows, startRow, groupSize, emptyColumnIndex, mergeInfo) {
                    // 检查组内空仓列的数据情况
                    const emptyValues = [];
                    for (let i = 0; i < groupSize; i++) {
                        const value = rows[startRow + i][emptyColumnIndex];
                        emptyValues.push(value);
                    }

                    // 过滤出非空值
                    const nonEmptyValues = emptyValues.filter(v => v && String(v).trim() !== '');
                    
                    // 只有在以下情况下才进行合并：
                    // 1. 所有值都为空，或
                    // 2. 所有非空值都完全相同
                    const shouldMerge = nonEmptyValues.length === 0 || 
                                       (nonEmptyValues.length > 0 && nonEmptyValues.every(v => String(v).trim() === String(nonEmptyValues[0]).trim()));

                    if (shouldMerge) {
                        // 保留第一个非空值（如果有的话），其他行清空
                        const firstNonEmptyValue = nonEmptyValues.length > 0 ? nonEmptyValues[0] : '';
                        
                        // 设置第一行的值
                        rows[startRow][emptyColumnIndex] = firstNonEmptyValue;
                        
                        // 清空其他行
                        for (let i = 1; i < groupSize; i++) {
                            rows[startRow + i][emptyColumnIndex] = '';
                        }

                        // 记录合并信息
                        mergeInfo.push({
                            startRow: startRow,
                            endRow: startRow + groupSize - 1,
                            startCol: emptyColumnIndex,
                            endCol: emptyColumnIndex,
                            type: 'empty'
                        });
                        return true;
                    }
                    return false;
                }

                // 测试用例1: 所有值都为空 - 应该合并
                let testRows1 = [
                    ['2024-01-01', '', '设备A'],
                    ['2024-01-01', '', '设备B'],
                    ['2024-01-01', '', '设备C']
                ];
                let mergeInfo1 = [];
                const merged1 = processEmptyColumnMerge(testRows1, 0, 3, 1, mergeInfo1);
                result.innerHTML += `测试1 (所有值为空): ${merged1 ? '✅ 正确合并' : '❌ 未合并'}\n`;

                // 测试用例2: 所有值相同 - 应该合并
                let testRows2 = [
                    ['2024-01-01', '是', '设备A'],
                    ['2024-01-01', '是', '设备B'],
                    ['2024-01-01', '是', '设备C']
                ];
                let mergeInfo2 = [];
                const merged2 = processEmptyColumnMerge(testRows2, 0, 3, 1, mergeInfo2);
                result.innerHTML += `测试2 (所有值相同): ${merged2 ? '✅ 正确合并' : '❌ 未合并'}\n`;

                // 测试用例3: 值不同 - 不应该合并
                let testRows3 = [
                    ['2024-01-01', '是', '设备A'],
                    ['2024-01-01', '否', '设备B'],
                    ['2024-01-01', '是', '设备C']
                ];
                let mergeInfo3 = [];
                const merged3 = processEmptyColumnMerge(testRows3, 0, 3, 1, mergeInfo3);
                result.innerHTML += `测试3 (值不同): ${!merged3 ? '✅ 正确不合并' : '❌ 错误合并'}\n`;

                // 验证数据保持完整
                const originalValues = ['是', '否', '是'];
                const currentValues = testRows3.map(row => row[1]);
                const dataIntact = JSON.stringify(originalValues) === JSON.stringify(currentValues);
                result.innerHTML += `测试3 数据完整性: ${dataIntact ? '✅ 数据保持完整' : '❌ 数据被修改'}\n`;

                result.className = 'test-result success';

            } catch (error) {
                result.innerHTML += `\n❌ 测试出错: ${error.message}\n`;
                result.className = 'test-result error';
            }
        }

        // 测试完整处理流程
        function testCompleteProcessing() {
            const result = document.getElementById('completeResult');
            result.innerHTML = '正在测试完整处理流程...\n';

            try {
                // 模拟完整的数据处理
                const mockData = [
                    ['序号', '日期', '设备名称', '故障处理情况', '空仓？'],
                    [1, '2024-01-01', '设备A', '已修复', '是'],
                    [2, '2024-01-01', '设备B', '待处理', '是'],
                    [3, '2024-01-02', '设备C', '已修复', '否'],
                    [4, '2024-01-02', '设备D', '已修复', '是']
                ];

                const headers = mockData[0];
                const dataRows = mockData.slice(1);

                // 应用列重排逻辑
                const filteredHeaders = [];
                const columnIndexMap = [];

                let dateColumnIndex = -1;
                let failureColumnIndex = -1;
                
                headers.forEach((header, index) => {
                    const headerStr = String(header).toLowerCase();
                    if (headerStr.includes('日期')) {
                        dateColumnIndex = filteredHeaders.length;
                    }
                    if (headerStr.includes('故障')) {
                        failureColumnIndex = filteredHeaders.length;
                    }
                    
                    columnIndexMap.push(index);
                    filteredHeaders.push(header);
                });

                // 插入维护保养情况列
                const maintenanceInsertIndex = Math.max(dateColumnIndex + 1, failureColumnIndex);
                filteredHeaders.splice(maintenanceInsertIndex, 0, '维护保养情况');
                columnIndexMap.splice(maintenanceInsertIndex, 0, -1);
                
                // 添加备注列
                filteredHeaders.push('备注');
                columnIndexMap.push(-2);

                result.innerHTML += `处理后的表头: ${filteredHeaders.join(', ')}\n\n`;

                // 创建表格显示结果
                let tableHTML = '<table><tr>';
                filteredHeaders.forEach(header => {
                    tableHTML += `<th>${header}</th>`;
                });
                tableHTML += '</tr>';

                // 处理数据行
                dataRows.forEach(row => {
                    tableHTML += '<tr>';
                    columnIndexMap.forEach(colIndex => {
                        let cellValue = '';
                        if (colIndex === -1) {
                            cellValue = ''; // 维护保养情况列
                        } else if (colIndex === -2) {
                            cellValue = '已解决'; // 备注列
                        } else {
                            cellValue = row[colIndex] || '';
                        }
                        tableHTML += `<td>${cellValue}</td>`;
                    });
                    tableHTML += '</tr>';
                });

                tableHTML += '</table>';
                result.innerHTML += tableHTML;

                result.innerHTML += '\n✅ 完整处理流程测试通过\n';
                result.className = 'test-result success';

            } catch (error) {
                result.innerHTML += `\n❌ 测试出错: ${error.message}\n`;
                result.className = 'test-result error';
            }
        }
    </script>
</body>
</html>
