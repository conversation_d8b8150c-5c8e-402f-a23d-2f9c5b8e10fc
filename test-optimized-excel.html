<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化合并逻辑测试文件生成器</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 12px;
        }
        th {
            background-color: #f2f2f2;
        }
        .has-data {
            background-color: #e8f5e8;
        }
        .no-data {
            background-color: #fff3cd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>优化合并逻辑测试文件生成器</h1>
        <p>生成用于测试优化后空仓列合并逻辑的Excel文件</p>
        
        <button onclick="createMixedDataFile()">生成混合数据测试文件</button>
        <p>同一日期下，有些行有空仓数据，有些行没有空仓数据</p>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        function createMixedDataFile() {
            // 创建混合数据测试文件：同一日期下有数据行和无数据行混合
            const data = [
                ['科陆流水线日常运维及故障处理情况'],
                ['序号', '日期', '设备名称', '故障处理情况', '空仓？', '记录人'],
                // 2024-01-01 组：混合数据
                [1, '2024-01-01', '设备A', '电源故障，已更换电源模块', '是', '张三'],    // 有数据
                [2, '2024-01-01', '设备B', '传感器异常，已重新校准', '', '李四'],        // 无数据
                [3, '2024-01-01', '设备C', '网络连接中断，已修复', '否', '王五'],        // 有数据
                [4, '2024-01-01', '设备D', '定期维护检查', '', '赵六'],               // 无数据
                [5, '2024-01-01', '设备E', '软件更新完成', '', '钱七'],               // 无数据
                // 2024-01-02 组：混合数据
                [6, '2024-01-02', '设备F', '硬件升级', '', '孙八'],                   // 无数据
                [7, '2024-01-02', '设备G', '故障排查', '是', '周九'],                 // 有数据
                [8, '2024-01-02', '设备H', '清洁维护', '', '吴十'],                   // 无数据
                // 2024-01-03 组：全部有数据
                [9, '2024-01-03', '设备I', '系统检测', '否', '郑十一'],               // 有数据
                [10, '2024-01-03', '设备J', '性能测试', '是', '王十二'],              // 有数据
                // 2024-01-04 组：全部无数据
                [11, '2024-01-04', '设备K', '例行检查', '', '李十三'],                // 无数据
                [12, '2024-01-04', '设备L', '预防性维护', '', '张十四']               // 无数据
            ];
            
            createAndDownloadExcel(data, 'test-optimized-merge.xlsx');
            showPreview(data, '优化合并逻辑测试文件');
        }

        function createAndDownloadExcel(data, filename) {
            // 创建工作簿
            const wb = XLSX.utils.book_new();
            
            // 创建工作表
            const ws = XLSX.utils.aoa_to_sheet(data);
            
            // 设置合并单元格（主标题行）
            ws['!merges'] = [
                { s: { r: 0, c: 0 }, e: { r: 0, c: 5 } }
            ];
            
            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(wb, ws, '运维日志');
            
            // 下载文件
            XLSX.writeFile(wb, filename);
        }

        function showPreview(data, title) {
            const result = document.getElementById('result');
            let html = `<h3>${title}</h3>`;
            html += '<p><strong>测试场景说明：</strong></p>';
            html += '<ul>';
            html += '<li><span style="background-color: #e8f5e8; padding: 2px 4px;">绿色背景</span>：空仓列有数据的行（应该保持不变）</li>';
            html += '<li><span style="background-color: #fff3cd; padding: 2px 4px;">黄色背景</span>：空仓列无数据的行（应该被合并）</li>';
            html += '</ul>';
            
            html += '<table>';
            
            data.forEach((row, index) => {
                html += '<tr>';
                if (index === 0) {
                    // 主标题行
                    html += `<td colspan="6" style="text-align: center; font-weight: bold; background-color: #e3f2fd;">${row[0]}</td>`;
                } else if (index === 1) {
                    // 列标题行
                    row.forEach(cell => {
                        html += `<th>${cell}</th>`;
                    });
                } else {
                    // 数据行
                    row.forEach((cell, cellIndex) => {
                        let style = '';
                        if (cellIndex === 4) { // 空仓列
                            const hasData = cell && String(cell).trim() !== '';
                            style = hasData ? 'background-color: #e8f5e8;' : 'background-color: #fff3cd;';
                        }
                        html += `<td style="${style}">${cell || ''}</td>`;
                    });
                }
                html += '</tr>';
            });
            
            html += '</table>';
            
            html += '<h4>期望的合并结果：</h4>';
            html += '<ul>';
            html += '<li><strong>2024-01-01组</strong>：保留行1和行3的空仓数据（"是"和"否"），合并行2、4、5的空数据</li>';
            html += '<li><strong>2024-01-02组</strong>：保留行7的空仓数据（"是"），合并行6、8的空数据</li>';
            html += '<li><strong>2024-01-03组</strong>：保留所有行的空仓数据（"否"和"是"），不进行合并</li>';
            html += '<li><strong>2024-01-04组</strong>：合并所有空数据行</li>';
            html += '</ul>';
            
            result.innerHTML = html;
        }
    </script>
</body>
</html>
