<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分析输出Excel文件</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #3498db;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #2980b9;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #27ae60;
            background-color: #e8f5e9;
        }
        .file-input {
            display: none;
        }
        .upload-icon {
            font-size: 48px;
            color: #3498db;
            margin-bottom: 20px;
        }
        .upload-text {
            font-size: 18px;
            color: #34495e;
            margin-bottom: 10px;
        }
        .upload-hint {
            font-size: 14px;
            color: #7f8c8d;
        }
        .analysis-result {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .result-section {
            margin-bottom: 20px;
        }
        .result-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .result-content {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
        }
        .data-row {
            padding: 8px;
            margin: 5px 0;
            background-color: #f1f3f4;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            font-weight: bold;
        }
        .error {
            color: #e74c3c;
            background-color: #fdf2f2;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            color: #27ae60;
            background-color: #f2f8f2;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 分析输出Excel文件</h1>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <div class="upload-icon">📄</div>
            <div class="upload-text">点击选择输出的Excel文件</div>
            <div class="upload-hint">支持 .xlsx 格式文件</div>
            <input type="file" id="fileInput" class="file-input" accept=".xlsx" onchange="analyzeFile(this.files[0])">
        </div>

        <div id="analysisResult" class="analysis-result" style="display: none;">
            <div class="result-section">
                <div class="result-title">📈 统计信息</div>
                <div id="statsContainer" class="stats"></div>
            </div>

            <div class="result-section">
                <div class="result-title">🔍 "空仓运维"数据详情</div>
                <div id="emptyDataDetails" class="result-content"></div>
            </div>

            <div class="result-section">
                <div class="result-title">📋 所有数据行概览</div>
                <div id="allDataOverview" class="result-content"></div>
            </div>
        </div>
    </div>

    <script>
        function analyzeFile(file) {
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    
                    // 获取第一个工作表
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    
                    // 转换为JSON数组
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                    
                    console.log('读取到的数据:', jsonData);
                    
                    analyzeData(jsonData);
                    
                } catch (error) {
                    console.error('文件读取错误:', error);
                    showError('文件读取失败: ' + error.message);
                }
            };
            reader.readAsArrayBuffer(file);
        }

        function analyzeData(data) {
            console.log('开始分析数据...');
            
            // 查找标题行
            let headerRowIndex = -1;
            let headers = [];
            
            for (let i = 0; i < Math.min(5, data.length); i++) {
                const row = data[i];
                if (row && row.some(cell => cell && String(cell).includes('空仓'))) {
                    headerRowIndex = i;
                    headers = row.map(cell => cell || '');
                    break;
                }
            }
            
            if (headerRowIndex === -1) {
                showError('未找到包含"空仓"的标题行');
                return;
            }
            
            console.log('找到标题行:', headerRowIndex, headers);
            
            // 查找"空仓？"列的索引
            let emptyColumnIndex = -1;
            for (let i = 0; i < headers.length; i++) {
                if (headers[i] && String(headers[i]).includes('空仓')) {
                    emptyColumnIndex = i;
                    break;
                }
            }
            
            if (emptyColumnIndex === -1) {
                showError('未找到"空仓？"列');
                return;
            }
            
            console.log('空仓列索引:', emptyColumnIndex);
            
            // 分析数据行
            const dataRows = data.slice(headerRowIndex + 1);
            const emptyDataRows = [];
            const allDataInfo = [];
            
            for (let i = 0; i < dataRows.length; i++) {
                const row = dataRows[i];
                const rowIndex = headerRowIndex + 1 + i;
                
                if (!row || row.length === 0) continue;
                
                const emptyValue = row[emptyColumnIndex];
                const hasEmptyData = emptyValue && String(emptyValue).trim() !== '';
                
                allDataInfo.push({
                    rowIndex: rowIndex + 1, // 1-based
                    emptyValue: emptyValue || '',
                    hasData: hasEmptyData,
                    fullRow: row
                });
                
                if (hasEmptyData && String(emptyValue).includes('空仓运维')) {
                    emptyDataRows.push({
                        rowIndex: rowIndex + 1, // 1-based
                        emptyValue: emptyValue,
                        fullRow: row
                    });
                }
            }
            
            console.log('找到的空仓运维数据:', emptyDataRows);
            console.log('所有数据行信息:', allDataInfo);
            
            // 显示分析结果
            displayAnalysisResult({
                totalRows: dataRows.length,
                emptyDataCount: emptyDataRows.length,
                emptyDataRows: emptyDataRows,
                allDataInfo: allDataInfo,
                headers: headers,
                emptyColumnIndex: emptyColumnIndex
            });
        }

        function displayAnalysisResult(analysis) {
            const resultDiv = document.getElementById('analysisResult');
            resultDiv.style.display = 'block';
            
            // 显示统计信息
            const statsContainer = document.getElementById('statsContainer');
            statsContainer.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${analysis.totalRows}</div>
                    <div class="stat-label">总数据行数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${analysis.emptyDataCount}</div>
                    <div class="stat-label">"空仓运维"数据行数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${analysis.emptyColumnIndex + 1}</div>
                    <div class="stat-label">"空仓？"列位置</div>
                </div>
            `;
            
            // 显示空仓运维数据详情
            const emptyDataDetails = document.getElementById('emptyDataDetails');
            if (analysis.emptyDataCount > 0) {
                let detailsHtml = '<div class="success">找到以下"空仓运维"数据:</div>';
                analysis.emptyDataRows.forEach((row, index) => {
                    detailsHtml += `
                        <div class="data-row highlight">
                            <strong>第${index + 1}条:</strong> 行${row.rowIndex} - "${row.emptyValue}"
                            <br><small>完整行数据: [${row.fullRow.map(cell => `"${cell || ''}"`).join(', ')}]</small>
                        </div>
                    `;
                });
                emptyDataDetails.innerHTML = detailsHtml;
            } else {
                emptyDataDetails.innerHTML = '<div class="error">❌ 未找到任何"空仓运维"数据！</div>';
            }
            
            // 显示所有数据行概览（只显示前50行）
            const allDataOverview = document.getElementById('allDataOverview');
            let overviewHtml = `<div>显示前50行数据概览 (共${analysis.totalRows}行):</div>`;
            
            const displayRows = analysis.allDataInfo.slice(0, 50);
            displayRows.forEach(rowInfo => {
                const className = rowInfo.hasData ? 'highlight' : '';
                overviewHtml += `
                    <div class="data-row ${className}">
                        行${rowInfo.rowIndex}: "${rowInfo.emptyValue}" ${rowInfo.hasData ? '✅' : '❌'}
                    </div>
                `;
            });
            
            if (analysis.totalRows > 50) {
                overviewHtml += `<div class="data-row">... 还有${analysis.totalRows - 50}行数据</div>`;
            }
            
            allDataOverview.innerHTML = overviewHtml;
        }

        function showError(message) {
            const resultDiv = document.getElementById('analysisResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `<div class="error">❌ ${message}</div>`;
        }

        // 拖拽上传功能
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                analyzeFile(files[0]);
            }
        });
    </script>
</body>
</html>
