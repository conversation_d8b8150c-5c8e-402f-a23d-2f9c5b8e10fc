<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>结果验证</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .file-input {
            margin: 20px 0;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            text-align: center;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 12px;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>修复结果验证</h1>
        <p>上传转换后的Excel文件来验证修复效果</p>
        
        <div class="file-input">
            <input type="file" id="fileInput" accept=".xlsx" onchange="handleFile(event)">
            <p>选择转换后的Excel文件</p>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function handleFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    
                    // 获取第一个工作表
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    
                    // 转换为JSON数据
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                    
                    verifyResult(jsonData);
                } catch (error) {
                    showError('文件读取失败: ' + error.message);
                }
            };
            reader.readAsArrayBuffer(file);
        }

        function verifyResult(data) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            
            let html = '<h2>验证结果</h2>';
            
            // 检查基本结构
            if (data.length < 3) {
                html += '<p class="error">❌ 文件结构不正确，行数不足</p>';
                resultDiv.innerHTML = html;
                return;
            }
            
            // 检查主标题行
            const titleRow = data[0];
            const isValidTitle = titleRow && titleRow[0] && titleRow[0].includes('科陆流水线');
            html += `<h3>1. 主标题行检查</h3>`;
            html += `<p class="${isValidTitle ? 'success' : 'error'}">`;
            html += `${isValidTitle ? '✅' : '❌'} 主标题: ${titleRow ? titleRow[0] : '未找到'}`;
            html += `</p>`;
            
            // 检查列标题
            const headerRow = data[1];
            html += `<h3>2. 列顺序检查</h3>`;
            html += `<p>列标题: ${headerRow ? headerRow.join(', ') : '未找到'}</p>`;
            
            // 验证维护保养情况列位置
            if (headerRow) {
                const dateIndex = headerRow.indexOf('日期');
                const maintenanceIndex = headerRow.findIndex(h => h && h.includes('维护保养'));
                const failureIndex = headerRow.findIndex(h => h && h.includes('故障'));
                
                html += `<p>日期列位置: ${dateIndex}</p>`;
                html += `<p>维护保养情况列位置: ${maintenanceIndex}</p>`;
                html += `<p>故障处理情况列位置: ${failureIndex}</p>`;
                
                const correctOrder = dateIndex < maintenanceIndex && maintenanceIndex < failureIndex;
                html += `<p class="${correctOrder ? 'success' : 'error'}">`;
                html += `${correctOrder ? '✅' : '❌'} 维护保养情况列位置${correctOrder ? '正确' : '错误'}`;
                html += `</p>`;
            }
            
            // 检查空仓列数据
            html += `<h3>3. 空仓列数据检查</h3>`;
            if (headerRow) {
                const emptyColumnIndex = headerRow.findIndex(h => h && h.includes('空仓'));
                if (emptyColumnIndex !== -1) {
                    html += `<p>空仓列位置: ${emptyColumnIndex}</p>`;
                    
                    // 检查数据行的空仓列值
                    const dataRows = data.slice(2);
                    const emptyColumnValues = dataRows.map(row => row[emptyColumnIndex] || '');
                    
                    html += `<p>空仓列数据: [${emptyColumnValues.join(', ')}]</p>`;
                    
                    // 按日期分组检查
                    const dateColumnIndex = headerRow.indexOf('日期');
                    if (dateColumnIndex !== -1) {
                        const groups = {};
                        dataRows.forEach(row => {
                            const date = row[dateColumnIndex];
                            const emptyValue = row[emptyColumnIndex] || '';
                            if (!groups[date]) groups[date] = [];
                            groups[date].push(emptyValue);
                        });
                        
                        html += `<h4>按日期分组的空仓列数据:</h4>`;
                        Object.entries(groups).forEach(([date, values]) => {
                            const uniqueValues = [...new Set(values.filter(v => v.trim() !== ''))];
                            const shouldMerge = uniqueValues.length <= 1;
                            const actuallyMerged = values.filter(v => v.trim() !== '').length <= 1;
                            
                            html += `<p>${date}: [${values.join(', ')}]`;
                            if (uniqueValues.length > 1) {
                                html += ` <span class="${!actuallyMerged ? 'success' : 'error'}">`;
                                html += `${!actuallyMerged ? '✅ 正确保持不同数据' : '❌ 错误合并了不同数据'}`;
                                html += `</span>`;
                            } else {
                                html += ` <span class="${actuallyMerged ? 'success' : 'warning'}">`;
                                html += `${actuallyMerged ? '✅ 正确合并相同数据' : '⚠️ 可以合并但未合并'}`;
                                html += `</span>`;
                            }
                            html += `</p>`;
                        });
                    }
                } else {
                    html += '<p class="error">❌ 未找到空仓列</p>';
                }
            }
            
            // 显示数据表格
            html += `<h3>4. 数据预览</h3>`;
            html += '<table>';
            data.forEach((row, index) => {
                html += '<tr>';
                if (index === 0) {
                    // 主标题行
                    html += `<td colspan="${row.length}" style="text-align: center; font-weight: bold; background-color: #e3f2fd;">${row[0] || ''}</td>`;
                } else {
                    // 其他行
                    row.forEach((cell, cellIndex) => {
                        const isHeader = index === 1;
                        const style = isHeader ? 'background-color: #f2f2f2; font-weight: bold;' : '';
                        html += `<td style="${style}">${cell || ''}</td>`;
                    });
                }
                html += '</tr>';
            });
            html += '</table>';
            
            resultDiv.innerHTML = html;
        }

        function showError(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `<p class="error">${message}</p>`;
        }
    </script>
</body>
</html>
